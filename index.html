<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
  </head>
  <body style="margin: 0; padding: 0; background: blue;">
    <h1 style="color: white; padding: 20px;">HTML TEST - Can you see this blue background?</h1>
    <div id="root" style="background: green; min-height: 200px; padding: 20px;">
      <p style="color: white;">This is the root div before React loads</p>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
