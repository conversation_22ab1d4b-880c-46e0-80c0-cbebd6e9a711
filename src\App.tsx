import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import Page from './components/page/page';
import Navbar from './components/navbar/navbar';

const theme = createTheme()

// Simple test layout component
const TestLayout = ({ children }) => {
  console.log('TestLayout rendering');

  try {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: 'lightgray'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'darkblue',
          color: 'white',
          padding: '10px 20px',
          height: '60px',
          display: 'flex',
          alignItems: 'center'
        }}>
          <h2>🧪 Test Layout Header</h2>
        </div>

        {/* Body */}
        <div style={{ display: 'flex', flex: 1 }}>
          {/* Sidebar */}
          <div style={{
            backgroundColor: 'darkgreen',
            color: 'white',
            width: '200px',
            padding: '20px'
          }}>
            <h3>Test Sidebar</h3>
            <p>Navigation items would go here</p>
          </div>

          {/* Content */}
          <div style={{
            flex: 1,
            padding: '20px',
            backgroundColor: 'white'
          }}>
            {children}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in TestLayout:', error);
    return (
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
        <h1>TestLayout Error!</h1>
        <p>Error: {error.message}</p>
      </div>
    );
  }
};

// Simple Navbar test component
const SimpleNavbar = ({ children }) => {
  console.log('SimpleNavbar rendering');

  try {
    return (
      <div style={{
        width: '200px',
        backgroundColor: 'darkblue',
        color: 'white',
        padding: '20px',
        minHeight: '100vh'
      }}>
        <h3>🧪 Simple Navbar</h3>
        {children}
      </div>
    );
  } catch (error) {
    console.error('Error in SimpleNavbar:', error);
    return (
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
        <h3>SimpleNavbar Error!</h3>
        <p>Error: {error.message}</p>
      </div>
    );
  }
};

function App() {
  console.log('Testing Simple Navbar');

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <div style={{ backgroundColor: 'lightcyan', minHeight: '100vh', display: 'flex' }}>
          <SimpleNavbar>
            <div style={{ padding: '10px', backgroundColor: 'rgba(255,255,255,0.2)' }}>
              <p>Simple navbar content</p>
            </div>
          </SimpleNavbar>

          <div style={{ flex: 1, padding: '20px' }}>
            <h1>🧪 Testing Simple Navbar</h1>
            <p>If you see this with a simple navbar on the left, the basic structure works.</p>
            <AppRoutes />
          </div>
        </div>
      </ThemeProvider>
    </Router>
  )
}

export default App

