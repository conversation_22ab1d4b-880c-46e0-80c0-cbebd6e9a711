import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import Page from './components/page/page';
import Navbar from './components/navbar/navbar';

const theme = createTheme()

// Simple test layout component
const TestLayout = ({ children }) => {
  console.log('TestLayout rendering');

  try {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: 'lightgray'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'darkblue',
          color: 'white',
          padding: '10px 20px',
          height: '60px',
          display: 'flex',
          alignItems: 'center'
        }}>
          <h2>🧪 Test Layout Header</h2>
        </div>

        {/* Body */}
        <div style={{ display: 'flex', flex: 1 }}>
          {/* Sidebar */}
          <div style={{
            backgroundColor: 'darkgreen',
            color: 'white',
            width: '200px',
            padding: '20px'
          }}>
            <h3>Test Sidebar</h3>
            <p>Navigation items would go here</p>
          </div>

          {/* Content */}
          <div style={{
            flex: 1,
            padding: '20px',
            backgroundColor: 'white'
          }}>
            {children}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in TestLayout:', error);
    return (
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
        <h1>TestLayout Error!</h1>
        <p>Error: {error.message}</p>
      </div>
    );
  }
};

function App() {
  console.log('Testing Navbar component');

  const testNavData = [
    {
      metadata: {
        title: 'Test Item',
        icon: <div>📋</div>,
        isParent: false,
        isBeta: false
      },
      route: { path: '/test' }
    }
  ];

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <div style={{ backgroundColor: 'lightcyan', minHeight: '100vh', display: 'flex' }}>
          <Navbar
            navigation={testNavData}
            isOpen={true}
            onChange={() => {}}
            selectedItem="/test"
          >
            <Navbar.Body>
              <div style={{ padding: '20px', backgroundColor: 'lightgreen' }}>
                <h3>🧪 Testing Navbar</h3>
                <p>Navbar body content</p>
              </div>
            </Navbar.Body>
            <Navbar.Footer>
              <div style={{ padding: '10px', backgroundColor: 'lightcoral' }}>
                Footer content
              </div>
            </Navbar.Footer>
          </Navbar>

          <div style={{ flex: 1, padding: '20px' }}>
            <h1>🧪 Testing Navbar Component</h1>
            <p>If you see this with a navbar on the left, the Navbar component works.</p>
            <AppRoutes />
          </div>
        </div>
      </ThemeProvider>
    </Router>
  )
}

export default App

