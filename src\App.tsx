import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';

const theme = createTheme()

function App() {
  console.log('Testing AppRoutes component');

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <div style={{
          padding: '20px',
          backgroundColor: 'mediumpurple',
          minHeight: '100vh',
          color: 'white',
          fontSize: '18px'
        }}>
          <h1>STEP 3: Testing AppRoutes</h1>
          <p>If you see this purple screen, the basic setup is working.</p>
          <p>Current path: {window.location.pathname}</p>
          <div style={{ marginTop: '20px', padding: '10px', backgroundColor: 'rgba(255,255,255,0.2)' }}>
            <AppRoutes />
          </div>
        </div>
      </ThemeProvider>
    </Router>
  )
}

export default App

