import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Container, Typography, Box } from '@mui/material';

const theme = createTheme()

function App() {
  return (
    <Router>
      <ThemeProvider theme={theme}>
        <Container maxWidth="lg">
          <Box sx={{ my: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Athena OTC POC
            </Typography>
            <Routes>
              <Route path="/" element={
                <Typography variant="body1">
                  Welcome to the Athena OTC Proof of Concept application!
                </Typography>
              } />
              <Route path="/dashboard" element={
                <Typography variant="h5">
                  Dashboard
                </Typography>
              } />
            </Routes>
          </Box>
        </Container>
      </ThemeProvider>
    </Router>
  )
}

export default App

