import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import { Layout } from './components/layout/layout';


const theme = createTheme()

function App() {
  return (
    <Router>
      <ThemeProvider theme={theme}>
        <Layout>
          <AppRoutes />
        </Layout>
      </ThemeProvider>
    </Router>
  )
}

export default App

