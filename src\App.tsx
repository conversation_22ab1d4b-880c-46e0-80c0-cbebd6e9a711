import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';

const theme = createTheme()

// Simple test layout component
const TestLayout = ({ children }) => {
  console.log('TestLayout rendering');

  try {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        backgroundColor: 'lightgray'
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: 'darkblue',
          color: 'white',
          padding: '10px 20px',
          height: '60px',
          display: 'flex',
          alignItems: 'center'
        }}>
          <h2>🧪 Test Layout Header</h2>
        </div>

        {/* Body */}
        <div style={{ display: 'flex', flex: 1 }}>
          {/* Sidebar */}
          <div style={{
            backgroundColor: 'darkgreen',
            color: 'white',
            width: '200px',
            padding: '20px'
          }}>
            <h3>Test Sidebar</h3>
            <p>Navigation items would go here</p>
          </div>

          {/* Content */}
          <div style={{
            flex: 1,
            padding: '20px',
            backgroundColor: 'white'
          }}>
            {children}
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error in TestLayout:', error);
    return (
      <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
        <h1>TestLayout Error!</h1>
        <p>Error: {error.message}</p>
      </div>
    );
  }
};

function App() {
  console.log('Testing simple layout');

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <TestLayout>
          <div style={{ padding: '20px' }}>
            <h1>🧪 Testing Simple Layout</h1>
            <p>If you see this with a header and sidebar, the basic layout structure works.</p>
            <AppRoutes />
          </div>
        </TestLayout>
      </ThemeProvider>
    </Router>
  )
}

export default App

