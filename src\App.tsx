import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import { Layout } from './components/layout/layout';


const theme = createTheme()

function App() {
  return (
    <Router>
      <ThemeProvider theme={theme}>
        <Layout>
          <div>
            <h1>Testing Layout Component</h1>
            <p>If you can see this, the Layout component is working.</p>
            <AppRoutes />
          </div>
        </Layout>
      </ThemeProvider>
    </Router>
  )
}

export default App

