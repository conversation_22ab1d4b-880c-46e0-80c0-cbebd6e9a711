import { useState, useEffect } from 'react'
import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
// import { AppRoutes } from './app-routes';
// import { Provider } from 'react-redux';
// import setupStore from './dataStore';
import { Layout } from './components/layout/layout'
// import { DrawerProvider } from './components/drawer/drawer-provider';
// import { ModalProvider } from './components/modals/modal-provider';

const theme = createTheme()

function App() {
  // useEffect(() => {
  //   http.checkEnableMockData();
  // }, [])

  return (
    <Router>
      <ThemeProvider theme={theme}>
        <Layout>
          <AppRoutes />
        </Layout >
      </ThemeProvider>
    </Router >
  )
}

export default App

