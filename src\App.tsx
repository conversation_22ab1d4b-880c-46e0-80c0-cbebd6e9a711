import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import { Layout } from './components/layout/layout';

const theme = createTheme()

function App() {
  return (
    <Router>
      <ThemeProvider theme={theme}>
        <Layout>
          <div style={{ padding: '20px' }}>
            <h1>Layout Component Working!</h1>
            <p>You should see the sidebar and header now.</p>
            <AppRoutes />
          </div>
        </Layout>
      </ThemeProvider>
    </Router>
  )
}

export default App

