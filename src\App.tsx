import { ThemeProvider, createTheme } from "@mui/material";
import { BrowserRouter as Router } from 'react-router-dom';
import { AppRoutes } from './app-routes';
import { Layout } from './components/layout/layout';


const theme = createTheme()

function App() {
  console.log('App component is rendering!');

  // Override body styles that might be causing issues
  document.body.style.display = 'block';
  document.body.style.placeItems = 'unset';

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      padding: '20px',
      backgroundColor: 'red',
      color: 'white',
      fontSize: '24px',
      zIndex: 9999
    }}>
      <h1>BASIC TEST - Can you see this?</h1>
      <p>If you can see this red text, React is working</p>
      <p>Check the browser console for logs</p>
      <p>Current time: {new Date().toLocaleTimeString()}</p>
    </div>
  )
}

export default App

