import { Routes, Route } from 'react-router-dom';

export const AppRoutes = () => {
    console.log('AppRoutes component rendering');

    try {
        return (
            <Routes>
                <Route path="/" element={
                    <div style={{ padding: '20px', backgroundColor: 'lightblue' }}>
                        <h2>✅ AppRoutes Working!</h2>
                        <p>Home page content</p>
                        <a href="/dashboard">Go to Dashboard</a>
                    </div>
                } />
                <Route path="/dashboard" element={
                    <div style={{ padding: '20px', backgroundColor: 'lightcyan' }}>
                        <h2>Dashboard Page</h2>
                        <p>Dashboard content</p>
                        <a href="/">Go to Home</a>
                    </div>
                } />
            </Routes>
        );
    } catch (error) {
        console.error('Error in AppRoutes:', error);
        return (
            <div style={{ padding: '20px', backgroundColor: 'red', color: 'white' }}>
                <h2>AppRoutes Error!</h2>
                <p>Error: {error.message}</p>
            </div>
        );
    }
}