import React from 'react';
import { RouteObject, useRoutes } from 'react-router-dom';
import ComplianceIcon from "@mui/icons-material/Shield";

// import { useAppSelector } from './dataStore';
// import { getURLSearchParams } from './dataStore/url-search';
// import { complianceRoutes } from './capabilities/compliance/routes';
// import { RouteProps } from './capabilities/types';

// Temporary type definition until we create the proper types file
interface RouteProps {
    route: RouteObject;
    permission: string;
    metadata: {
        title: string;
        description: string;
        icon: React.ReactNode;
        isParent: boolean;
        tags: string[];
    };
}

const root = "/";

const complianceRoutes = [
    {
        route: {
            path: "/",
            element: <div style={{ padding: '20px' }}>
                <h2>Welcome to Athena OTC POC</h2>
                <p>Navigate to <a href="/dashboard">/dashboard</a> to see the dashboard.</p>
            </div>
        },
        permission: 'HOME',
        metadata: {
            title: 'Home',
            description: 'Home page',
            icon: <ComplianceIcon />,
            isParent: false,
            tags: ['home']
        }
    },
    {
        route: {
            path: "/dashboard",
            element: <div style={{ padding: '20px' }}>
                <h2>Dashboard</h2>
                <p>This is the dashboard page.</p>
            </div>
        },
        permission: 'MY OZONE',
        metadata: {
            title: 'My Ozone',
            description: 'Dashboard',
            icon: <ComplianceIcon />,
            isParent: true,
            tags: ['status', 'dashboard', 'compliance', 'health']
        }
    }]


export const getCoreRoutes = (): RouteObject[] => {
    return complianceRoutes.map(({ route }) => route);
}

export const AppRoutes = () => {
    // const {status, error} = useStaticCloudAuth()
    const routes = useRoutes(getCoreRoutes());

    // const urlSearch = useAppSelector(getURLSearchParams);
    // const location = useLocation();
    // const navigate = useNavigate();

    return routes;
}