import React from 'react';
import { RouteObject, useLocation, useNavigate, useRoutes } from 'react-router-dom';
import ComplianceIcon from "@mui/icons-material/Shield";

import { useAppSelector } from './dataStore';
import { getURLSearchParams } from './dataStore/url-search';
// import { complianceRoutes } from './capabilities/compliance/routes';
import { RouteProps } from './capabilities/types';

const root = "/";

const complianceRoutes = [
    {
        route: {
            path: `${root}/dashboard`,
            element: <div>Dashboard</div>
        },
        permission: 'MY OZONE',
        metadata: {
            title: 'My Ozone',
            description: 'Dashboard',
            icon: <ComplianceIcon />,
            isParent: true,
            tags: ['status', 'dashboard', 'compliance', 'health']
        }
    }]


export const getCoreRoutes = (): RouteObject[] => {
    return [complianceRoutes].reduce((newRoutes: RouteObject[], routes: RouteProps[]) => {
        routes.forEach(({ route }) => {
            newRoutes.push(route)
        })

        return newRoutes

    }, [])
}

export const AppRoutes = () => {
    // const {status, error} = useStaticCloudAuth()
    const routes = useRoutes(getCoreRoutes());


    // const urlSearch = useAppSelector(getURLSearchParams);
    const location = useLocation();
    const navigate = useNavigate();

    return routes;
}