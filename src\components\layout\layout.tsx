import React, { FC, PropsWithChildren, useState } from "react"
import { localStorageUtil } from "../../utils/local-storage"
// import { complianceRoutes } from "../../capabilities/compliance/routes";
import ComplianceIcon from "@mui/icons-material/Shield";
import _ from 'lodash';
import Page from "../page/page.tsx";
import { Chip, IconButton } from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu"
import Navbar from "../navbar/navbar.tsx";
// import { UserMenu } from "../user-menu/user-menu";

const root = "/";

const complianceRoutes = [
    {
        route: {
            path: `${root}/dashboard`,
            element: <div>Dashboard</div>
        },
        permission: 'MY OZONE',
        metadata: {
            title: 'My Ozone',
            description: 'Dashboard',
            icon: <ComplianceIcon />,
            isParent: true,
            isBeta: false,
            tags: ['status', 'dashboard', 'compliance', 'health']
        }
    }]



export const Layout: FC<PropsWithChildren> = ({ children }) => {
    const isCurrentlyCollapsed = localStorageUtil.get('ui-sidebar-collapsed', true) || false;
    const [isCollapsed, setIsCollapsed] = useState(isCurrentlyCollapsed);
    const allertChannel = new BroadcastChannel('alert_channel');

    allertChannel.onmessage = (event: any) => {
        console.log(event);
    }

    const navData = complianceRoutes;
    const currentPath = window.location.pathname;
    const PathExists = _.findIndex(navData, { route: { path: currentPath } })

    // Temporarily disable redirect to debug layout
    // if (PathExists === -1) {
    //     window.location.replace(navData[0].route.path || "")
    // }

    const toggleSidebar = (newState: boolean) => {
        setIsCollapsed(newState);
        localStorageUtil.set('ui-sidebar-collapsed', newState.toString(), false)
    }

    return <Page className='font-Optimist layout' data-testid="layout">
        <Page.Header className="flex grow items-center justify-between h-12 p-1">
            <div className="flex gap-3">
                <IconButton onClick={() => toggleSidebar(!isCollapsed)}>
                    <MenuIcon />
                </IconButton>

                <div className="flex text-slate-500 gap-1 items-center">
                    <div className="w-16 py-1">Logo</div>
                    Cloud Safety
                </div>
            </div>
            {/* <UserMenu /> */}
        </Page.Header>
        <Page.Body className="flex">
            <Navbar navigation={navData} isOpen={!isCollapsed} onChange={toggleSidebar} selectedItem={currentPath}>
                <Navbar.Body key="navbar-body">
                    {navData.map(({ metadata: { isParent, isBeta, icon, title }, route: { path } }, index) =>
                        <div key={`${path}-${index}`}>
                            <Navbar.Item isParent={isParent} id={path} to={path} key={`${path}-${index}`}>
                                {icon}
                                {isCollapsed ? null : title}
                                {isBeta && !isCollapsed &&
                                    <Chip label="BETA" color="warning" size="small" sx={{ marginLeft: "-5px", marginRight: "5px" }}></Chip>
                                }
                            </Navbar.Item>
                            <Navbar.Spacer key={`${path}-${index}-spacer}`} />
                        </div>)}
                </Navbar.Body>
                <Navbar.Footer />
            </Navbar>
            <div className={`${isCollapsed ? 'left-16' : 'left-64'} block border-t fixed top-12 right-0 p-4 bottom-0 transition-all overflow-y-auto bg-gray-100`}>{children}</div>
        </Page.Body>
    </Page>

}