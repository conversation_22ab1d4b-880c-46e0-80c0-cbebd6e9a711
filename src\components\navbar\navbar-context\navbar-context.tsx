import React from 'react'

import createGenericContext from './../../../utils/generic-context'

export interface NavbarContextType {
    getHeaderProps?: (additionalProps?: object) => object;
    getBodyProps?: (additionalProps?: object) => object;
    getFooterProps?: (additionalProps?: object) => object;
    getSpacerProps?: (additionalProps?: object) => object;
    getItemProps?: (additionalProps?: object) => object;
    isOpen: boolean;
    onChange: (state: boolean) => void;
    onSelect: (id: string) => void;
    selected: string;
    rest?: any;
}

export const {
    GenericContextProvider: NavbarContextProvider,
    useGenericContext: useNavbarContext,
    GenericContextConsumer: NavbarContextComponent
} = createGenericContext<NavbarContextType>({
    errorMessage: 'Navbar subcomponents cant be rendered outside the Page component'
})