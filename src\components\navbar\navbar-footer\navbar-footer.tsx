import React, { FC, ReactNode } from 'react';
import "./navbar-footer.scss"
import { useNavbarContext } from '../navbar-context/navbar-context';
import ExpandIcon from "@mui/icons-material/ArrowForward";
import CollapseIcon from "@mui/icons-material/ArrowBack";

export interface FooterProps {
    className?: string;
    rest?: any;
}

const NavbarFooter: FC<FooterProps> = ({ className = '',  ...rest }) => {
    const { getFooterProps, isOpen, onChange } = useNavbarContext();
    if (!getFooterProps) {
        throw new Error('Navbar.Footer requires getFooterProps to be defined')
    }

    return <div {...getFooterProps} {...rest} className={`navbar-footer
    absolute flex justify-end w-full px-5 items-center border-t h-16 bottom-0 transition-all cursor-pointer
    ${isOpen ? 'navbar-footer--open' : 'navbar-footer--closed'}
    ${className}`} data-testid='navbar-footer' onClick={() => onChange(isOpen)}>
        {isOpen ? <CollapseIcon /> : <ExpandIcon />}
    </div>
}

export default NavbarFooter;