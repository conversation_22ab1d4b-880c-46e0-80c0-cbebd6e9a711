import React, { FC, ReactNode } from 'react';
import "./navbar-item.scss"
import { useNavbarContext } from '../navbar-context/navbar-context';
import { NavLink } from 'react-router-dom';

export interface ItemProps {
    id: string;
    isParent?: boolean;
    to?: string;
    className?: string;
    children: ReactNode;
    key: string;
    rest?: any;
}

const NavbarItem: FC<ItemProps> = ({ isParent = false, id = '', to = '/', className = '', children, ...rest }) => {
    const { getItemProps, isOpen, selected, onSelect } = useNavbarContext();

    if (!getItemProps) {
        throw new Error('Navbar.Item requires getFooterProps to be defined')
    }

    const isSelected = selected && (id === selected || (isParent && id === selected.split('/')[0]))

    const sharedClass = `grid grid-flow-col items-center auto-cols-max gap-3 ${isOpen ? 'ml-2' : "ml-1"}`;
    const groupClass = `h-16 border-t hover:text-white hover:bg-cs-blue-500 [&>svg]:fill-cs-blue-500 [&>svg]:hover:fill-white hover:border-cs-blue-500 bg-cs-blue-500 border-t border-cs-blue-500 !text-white h-16 [&>svg]:fill-cs-blue-10`;
    const subItemClass = `h-12 border-l-2 mr-2 ${isOpen ? 'ml-5' : 'ml-2'} ${isSelected ?
        'bg-cs-blue-26  border-cs-blue-500 rounded-r-full transition-all duration-700' :
        'border-slate-200 hover:bg-slate-100 hover:border-cs-blue-500 rounded-r-full'}`;

    const itemClass = `${sharedClass} ${isParent ? groupClass : subItemClass}`



    return <li
        {...getItemProps}
        className={`${isSelected ? 'navbar-item--selected' : 'navbar-tiem'} ${className}`}
        data-testid='navbar-item'
        onClick={() => onSelect(id)}
        {...rest}>
        {children && (<NavLink to={to} className={itemClass}>
            {children}
        </NavLink>)}
    </li>

}

export default NavbarItem;