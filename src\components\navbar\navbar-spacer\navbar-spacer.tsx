import React, { FC, ReactNode } from 'react';
import "./navbar-spacer.scss"
import { useNavbarContext } from '../navbar-context/navbar-context';


export interface SpacerProps {
    className?: string;
    key: string;
    rest?: any;
}

const NavbarSpacer: FC<SpacerProps> = ({ className = '', ...rest }) => {
    const { getSpacerProps, isOpen } = useNavbarContext();
    if (!getSpacerProps) {
        throw new Error('Navbar.Spacer requires getFooterProps to be defined')
    }

    return (
        <li
            {...getSpacerProps}
            className={`navbar-spacer  border-l-2border-slate-200 h-3 mr-2  ${isOpen ? 'navbar-spacer--open' : 'navbar-spacer ml-2'} ${className}`}
            data-testid='navbar-spacer'
            {...rest}
        />)
}

export default NavbarSpacer;