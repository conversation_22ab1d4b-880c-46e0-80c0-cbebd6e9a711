import { ForwardRefExoticComponent, ReactNode, RefAttributes, forwardRef, useState } from "react";
import { RouteProps } from "react-router-dom";
import { NavbarContextComponent, NavbarContextProvider } from "./navbar-context/navbar-context";
import NavbarItem from "./navbar-item/navbar-item";
import NavbarFooter from "./navbar-footer/navbar-footer";
import NavbarBody from "./navbar-body/navbar-body";
import NavbarSpacer from "./navbar-spacer/navbar-spacer";




export interface NavbarProps {
    isOpen: boolean;
    navigation: any[];
    onChange: (state: any) => void
    className?: string;
    selectedItem?: string;
    children: ReactNode;
    rest?: any;
}

export interface PageComposite extends ForwardRefExoticComponent<NavbarProps & RefAttributes<HTMLDivElement>> {
    Context: typeof NavbarContextComponent;
    Item: typeof NavbarItem;
    Body: typeof NavbarBody;
    Footer: typeof NavbarFooter;
    Spacer: typeof NavbarSpacer;
}

const Navbar = forwardRef<HTMLDivElement, NavbarProps>(
    ({ isOpen = true, navigation = [], onChange = () => { }, children, selectedItem = '', ...rest }: NavbarProps, ref) => {
        const [selected, setSelected] = useState<string>(selectedItem)

        const getHeaderProps = () => ({});
        const getBodyProps = () => ({});
        const getFooterProps = () => ({});
        const getItemProps = () => ({});
        const getSpacerProps = () => ({});

        const onSelect = (id: string) => {
            setSelected(id)
        }

        const props = {
            getHeaderProps,
            getBodyProps,
            getFooterProps,
            getSpacerProps,
            getItemProps,
            isOpen,
            selected,
            onChange,
            onSelect,
            ...rest
        }

        return <NavbarContextProvider value={props}>
            <div data-testid='navbar' ref={ref} {...rest}
                className={`${isOpen ? 'w-64 navbar--open' : 'w-16 navbar--closed'} border-b fixed top-12 left-0 bottom-0`}>
                {children}
            </div>
        </NavbarContextProvider>
    }) as PageComposite;

Navbar.Context = NavbarContextComponent;
Navbar.Item = NavbarItem;
Navbar.Body = NavbarBody;
Navbar.Footer = NavbarFooter;
Navbar.Spacer = NavbarSpacer;
Navbar.displayName = 'Navbar'

export default Navbar

