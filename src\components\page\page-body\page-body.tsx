import React, { FC, ReactNode } from 'react';
import { usePageContext } from '../page-context/page-context';

export interface BodyProps {
    className?: string;
    children: ReactNode;
    rest?: any;
}

const PageBody: FC<BodyProps> = ({ className = '', children, ...rest }) => {
    const { getBodyProps } = usePageContext();
    if (!getBodyProps) {
        throw new Error('Page.Body requires getHeaderProps to be defined')
    }

    return <div {...getBodyProps} {...rest} className={`page-body ${className}`}>
        {children}
    </div>
}

export default PageBody;