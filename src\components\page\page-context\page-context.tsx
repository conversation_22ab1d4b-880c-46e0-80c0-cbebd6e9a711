import React from 'react'

import createGenericContext from '../../../utils/generic-context';

export interface PageContextType {
    getHeaderProps?: (additionalProps?: object) => object;
    getBodyProps?: (additionalProps?: object) => object;
    getFooterProps?: (additionalProps?: object) => object;
}

export const {
    GenericContextProvider: PageContextProvider,
    useGenericContext: usePageContext,
    GenericContextConsumer: PageContextComponent
} = createGenericContext<PageContextType>({
    errorMessage: 'Page subcomponents cant be rendered outside the Page component'
})