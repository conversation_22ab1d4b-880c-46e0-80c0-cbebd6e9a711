import React, { FC, ReactNode } from 'react';
import { usePageContext } from './page-context/page-context';

import "./page-footer.scss"

export interface FooterProps {
    className?: string;
    children: ReactNode;
    rest?: any;
}

const PageFooter: FC<FooterProps> = ({ className = '', children, ...rest }) => {
    const { getFooterProps } = usePageContext();
    if (!getFooterProps) {
        throw new Error('Page.Footer requires getFooterProps to be defined')
    }

    return <div {...getFooterProps} {...rest} className={`page-footer ${className}`}>
        {children}
    </div>
}

export default PageFooter;