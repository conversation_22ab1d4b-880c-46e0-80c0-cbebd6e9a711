import React, { FC, ReactNode } from 'react';

import { usePageContext } from '../page-context/page-context';

import "./page-header.scss"

export interface HeaderProps {
    title?: string;
    className?: string;
    children: ReactNode;
    rest?: any;
}

const PageHeader: FC<HeaderProps> = ({ title = '', className = '', children, ...rest }) => {
    const { getHeaderProps } = usePageContext();
    if (!getHeaderProps) {
        throw new Error('Page.Header requires getHeaderProps to be defined')
    }

    return <div {...getHeaderProps} {...rest} className={`page-header ${className}`}>
        {title ? <p>{title}</p> : children}
    </div>
}

export default PageHeader;