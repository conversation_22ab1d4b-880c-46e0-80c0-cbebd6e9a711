import { ForwardRefExoticComponent, ReactNode, RefAttributes, forwardRef } from "react";
import { PageContextComponent, PageContextProvider } from "./page-context/page-context";
import PageHeader from "./page-header/page-header";
import PageBody from "./page-body/page-body";
import PageFooter from "./page-footer/page-footer";


export interface PageProps {
    className?: string;
    children: ReactNode;
    type?: string;
    rest?: any;
}

export interface PageComposite extends ForwardRefExoticComponent<PageProps & RefAttributes<HTMLDivElement>> {
    Context: typeof PageContextComponent;
    Header: typeof PageHeader;
    Body: typeof PageBody;
    Footer: typeof PageFooter;
}

const Page = forwardRef<HTMLDivElement, PageProps>(({ children, type = 'standard', ...rest }: PageProps, ref) => {
    const getHeaderProps = () => ({});
    const getBodyProps = () => ({});
    const getFooterProps = () => ({});

    const props = {
        getHeaderProps,
        getBodyProps,
        getFooterProps,
        ...rest
    }

    return <PageContextProvider value={props}>
        <div data-testid='page' ref={ref} {...rest}>
            {children}
        </div>
    </PageContextProvider>
}) as PageComposite;

Page.Context = PageContextComponent;
Page.Header = PageHeader;
Page.Body = PageBody;
Page.Footer = PageFooter;
Page.displayName = 'Page'

export default Page

