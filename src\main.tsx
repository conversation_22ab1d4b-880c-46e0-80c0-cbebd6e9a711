console.log('main.tsx is starting to load!');

try {
  console.log('Importing React...');
  const { StrictMode } = await import('react');
  console.log('React imported successfully');

  console.log('Importing ReactDOM...');
  const { createRoot } = await import('react-dom/client');
  console.log('ReactDOM imported successfully');

  console.log('Importing CSS...');
  await import('./index.css');
  console.log('CSS imported successfully');

  console.log('Importing App...');
  const AppModule = await import('./App.tsx');
  const App = AppModule.default;
  console.log('App imported successfully');

  console.log('Getting root element...');
  const rootElement = document.getElementById('root');
  console.log('Root element:', rootElement);

  if (!rootElement) {
    throw new Error('Root element not found!');
  }

  console.log('Creating React root...');
  const root = createRoot(rootElement);
  console.log('React root created successfully');

  console.log('Rendering App...');
  root.render(
    StrictMode({ children: App({}) })
  );
  console.log('App rendered successfully!');

} catch (error) {
  console.error('Error in main.tsx:', error);

  // Fallback: show error in the DOM
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="background: red; color: white; padding: 20px;">
        <h1>JavaScript Error!</h1>
        <p>Error: ${error.message}</p>
        <p>Check the browser console for more details.</p>
      </div>
    `;
  }
}
