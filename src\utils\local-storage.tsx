import type { ReactElement, FC } from 'react';
import React, { createContext, useContext } from 'react'

export interface ContextType<T> {
    initialContext?: T | undefined;
    errorMessage?: string;
}

export default function createGenericContext<T>({ initialContext, errorMessage }: ContextType<T>) {
    const GenericContext = createContext(initialContext);
    const useGenericContext = () => {
        const context = useContext(GenericContext);
        if (!context) {
            throw new Error(errorMessage)
        }
        return context
    };

    const GenericContextConsumer: FC<{ children(props: T): ReactElement }> = ({ children }) => {
        const context = useGenericContext();
        return children(context)
    }

    return {
        useGenericContext,
        GenericContextProvider: GenericContext.Provider,
        GenericContextConsumer
    }
}