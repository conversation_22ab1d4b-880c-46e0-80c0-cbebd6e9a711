// Local Storage Utility
export const localStorageUtil = {
    get: (key: string, defaultValue?: any) => {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error(`Error getting item from localStorage: ${key}`, error);
            return defaultValue;
        }
    },

    set: (key: string, value: any, stringify: boolean = true) => {
        try {
            const valueToStore = stringify ? JSON.stringify(value) : value;
            localStorage.setItem(key, valueToStore);
        } catch (error) {
            console.error(`Error setting item in localStorage: ${key}`, error);
        }
    },

    remove: (key: string) => {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error(`Error removing item from localStorage: ${key}`, error);
        }
    },

    clear: () => {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('Error clearing localStorage', error);
        }
    }
};